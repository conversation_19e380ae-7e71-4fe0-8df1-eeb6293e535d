{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-20T01:29:16.980Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-20T01:29:16.987Z"}
{"level":"info","message":"Socket connected: qKBxB7Pl_GorthmcAAAB","remoteAddress":"::1","service":"notification-service","socketId":"qKBxB7Pl_GorthmcAAAB","timestamp":"2025-07-20T01:30:09.079Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qKBxB7Pl_GorthmcAAAB","timestamp":"2025-07-20T01:30:09.084Z","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"data":{"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","message":"Your analysis is ready!","resultId":"e10e90c4-0384-4671-bd5e-00708cbfc3ef","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","service":"notification-service","socketCount":1,"timestamp":"2025-07-20T01:30:31.412Z"}
{"jobId":"03e18fa1-6f4e-420d-81fb-16f7eb9cd05c","level":"info","message":"Analysis complete notification processed","resultId":"e10e90c4-0384-4671-bd5e-00708cbfc3ef","sent":true,"service":"notification-service","timestamp":"2025-07-20T01:30:31.413Z","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","reason":"client namespace disconnect","remainingConnections":0,"service":"notification-service","socketId":"qKBxB7Pl_GorthmcAAAB","timestamp":"2025-07-20T01:30:39.303Z","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}

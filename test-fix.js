const axios = require('axios');

// Configuration
const AUTH_SERVICE_URL = 'http://localhost:3001';
const ASSESSMENT_SERVICE_URL = 'http://localhost:3003';

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

// Sample assessment data
const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30,
    openness: 80
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 65,
    perseverance: 70,
    honesty: 75,
    zest: 60,
    love: 55,
    kindness: 68,
    socialIntelligence: 72,
    teamwork: 65,
    fairness: 70,
    leadership: 60,
    forgiveness: 55,
    humility: 50,
    prudence: 65,
    selfRegulation: 70,
    appreciationOfBeauty: 75,
    gratitude: 80,
    hope: 70,
    humor: 65,
    spirituality: 45
  }
};

async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 0,
      data: error.response?.data || { error: error.message }
    };
  }
}

async function testLogin() {
  console.log('\n=== LOGIN TEST ===');
  
  const result = await makeRequest('POST', `${AUTH_SERVICE_URL}/auth/login`, testUser);
  
  console.log('Login Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  if (result.success && result.data.data?.token) {
    return {
      token: result.data.data.token,
      userId: result.data.data.user.id
    };
  }
  
  return null;
}

async function testSubmitAssessment(token) {
  console.log('\n=== SUBMIT ASSESSMENT TEST ===');
  
  const result = await makeRequest('POST', `${ASSESSMENT_SERVICE_URL}/assessments/submit`, assessmentData, {
    'Authorization': `Bearer ${token}`
  });
  
  console.log('Submit Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));
  
  if (result.success && result.data.data?.jobId) {
    return result.data.data.jobId;
  }
  
  return null;
}

async function main() {
  console.log('🧪 Testing Analysis Worker Fix...');
  
  // Step 1: Login
  const loginResult = await testLogin();
  if (!loginResult) {
    console.log('❌ Login failed, cannot continue');
    return;
  }
  
  console.log(`✅ Login successful, User ID: ${loginResult.userId}`);
  
  // Step 2: Submit assessment
  const jobId = await testSubmitAssessment(loginResult.token);
  if (!jobId) {
    console.log('❌ Assessment submission failed');
    return;
  }
  
  console.log(`✅ Assessment submitted, Job ID: ${jobId}`);
  console.log('\n📋 Now check the analysis-worker and archive-service logs to see if the routing issue is fixed!');
}

main().catch(console.error);
